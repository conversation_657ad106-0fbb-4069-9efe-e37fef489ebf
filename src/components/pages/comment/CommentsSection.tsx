import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { MessageSquare, Edit, Trash2, Plus, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { LoadingSpinner } from '@/components/custom-ui/loading';
import { useToast } from '@/components/custom-ui/toast';
import { useBusinessContext } from '@/context/BusinessContext';
import { useUserContext } from '@/context/UserContext';
import type { IComment } from '@/types';
import { CommentForm } from './CommentForm';

interface CommentsSectionProps {
  assignmentId: number;
}

export function CommentsSection({ assignmentId }: CommentsSectionProps) {
  const [comments, setComments] = useState<IComment[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingComment, setEditingComment] = useState<IComment | null>(null);
  const [deletingComment, setDeletingComment] = useState(0);

  const { user } = useUserContext();
  const { fetchActions: { fetchEntity }, entityActions: { deleteEntity } } = useBusinessContext();
  const { addToast } = useToast();

  useEffect(() => {
    fetchComments();
  }, [assignmentId]);

  const fetchComments = async () => {
    setLoading(true);
    try {
      const response = await fetchEntity<{ results: IComment[] }>(`/v1/comments/assignment/${assignmentId}`);
      console.log('Comments fetched successfully:', response.data);
      if (response.data) {
        const sortedComments = (response.data.results || []).sort(
          (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
        setComments(sortedComments);
      } else if (response.error) {
        console.error('Failed to fetch comments:', response.error);
        addToast({
          type: "error",
          title: "Failed to load comments",
          description: response.error
        });
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
      addToast({
        type: "error",
        title: "Failed to load comments",
        description: "An unexpected error occurred"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditClick = (comment: IComment) => {
    setEditingComment(comment);
    setIsEditDialogOpen(true);
  };

  const handleDelete = async (commentId: number) => {
    const deleteResponse = await deleteEntity(`/v1/comments/${commentId}`);
    if (deleteResponse.error) {
      addToast({
        type: "error",
        title: "Delete failed",
        description: deleteResponse.error || "Failed to delete comment"
      });
      return;
    }

    addToast({
      type: "success",
      title: "Comment deleted",
      description: "The comment has been deleted successfully."
    });
    fetchComments();
    setDeletingComment(0);
  }

  const handleFormSuccess = () => {
    fetchComments();
    setIsCreateDialogOpen(false);
    setIsEditDialogOpen(false);
    setEditingComment(null);
  };

  const canEditComment = (comment: IComment) => {
    return user && user.id === comment.consultantId;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <MessageSquare className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            Comments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <MessageSquare className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              Comments ({comments.length})
            </CardTitle>
            <Button onClick={() => setIsCreateDialogOpen(true)} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Comment
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {comments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No comments yet.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map((comment) => (
                <div
                  key={comment.id}
                  className="border rounded-lg p-4 bg-muted/30 hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-full">
                        <User className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                      </div>
                      <div className='flex flex-col flex-start text-left'>
                        <p className="font-medium text-sm">
                          {comment.consultant?.name || 'Unknown Consultant'}
                        </p>
                        <span className="text-xs text-muted-foreground">
                          {format(new Date(comment.createdAt), "EEE, dd MMM yyyy 'at' h:mm a")}
                          {comment.updatedAt !== comment.createdAt && (
                            <span className="ml-1">(edited)</span>
                          )}
                        </span>
                      </div>
                    </div>
                    {canEditComment(comment) && (
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditClick(comment)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDeletingComment(comment.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                  <p className="text-sm whitespace-pre-wrap text-left">{comment.content}</p>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card >

      {/* Create Comment Dialog */}
      < Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen} >
        <DialogContent className="w-[50vw] max-h-[90vh] overflow-y-auto" aria-describedby={undefined}>
          <DialogHeader>
            <DialogTitle>Add Comment</DialogTitle>
          </DialogHeader>
          <CommentForm
            assignmentId={assignmentId}
            onCancel={() => setIsCreateDialogOpen(false)}
            onSuccess={handleFormSuccess}
            mode="create"
          />
        </DialogContent>
      </Dialog >

      {/* Edit Comment Dialog */}
      < Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen} >
        <DialogContent className="w-[50vw] max-h-[90vh] overflow-y-auto" aria-describedby={undefined}>
          <DialogHeader>
            <DialogTitle>Edit Comment</DialogTitle>
          </DialogHeader>
          {editingComment && (
            <CommentForm
              initialData={editingComment}
              assignmentId={assignmentId}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setEditingComment(null);
              }}
              onSuccess={handleFormSuccess}
              mode="edit"
            />
          )}
        </DialogContent>
      </Dialog >
      <Dialog open={deletingComment > 0} onOpenChange={() => setDeletingComment(0)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Comment</DialogTitle>
          </DialogHeader>
          <p>Are you sure you want to delete this comment?</p>
          <div className="flex justify-end gap-2 mt-6">
            <Button
              variant="outline"
              onClick={() => setDeletingComment(0)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleDelete(deletingComment)}
            >
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
