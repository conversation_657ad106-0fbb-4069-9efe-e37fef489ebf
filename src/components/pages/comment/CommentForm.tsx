import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useUserContext } from "@/context/UserContext";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/custom-ui/textarea";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/custom-ui/toast";
import type { IComment, ICommentCreatePayload, ICommentUpdatePayload } from "@/types";
import { useBusinessContext } from "@/context/BusinessContext";

export const commentFormSchema = z.object({
  id: z.number().optional(),
  assignmentId: z.number(),
  consultantId: z.number(),
  content: z.string().min(1, "Content is required").max(1000, "Content must be less than 1000 characters"),
});

interface CommentFormProps {
  initialData?: IComment;
  assignmentId: number;
  onCancel: () => void;
  onSuccess?: () => void;
  mode?: "create" | "edit";
}

export function CommentForm({ 
  initialData, 
  assignmentId, 
  onCancel, 
  onSuccess, 
  mode = "create" 
}: CommentFormProps) {
  const { user } = useUserContext();
  const {
    entityActions: {
      createEntity,
      updateEntity
    }
  } = useBusinessContext();
  const { addToast } = useToast();

  const form = useForm<z.infer<typeof commentFormSchema>>({
    resolver: zodResolver(commentFormSchema),
    defaultValues: {
      id: initialData?.id || 0,
      assignmentId: assignmentId,
      consultantId: initialData?.consultantId || user?.id || 0,
      content: initialData?.content || "",
    },
  });

  const handleSubmit = async (data: z.infer<typeof commentFormSchema>) => {
    if (!user) {
      addToast({
        type: "error",
        title: "Authentication required",
        description: "You must be logged in to create comments."
      });
      return;
    }

    if (mode === "create") {
      await handleCreate(data);
    } else {
      await handleEdit(data);
    }
  };

  const handleCreate = async (data: z.infer<typeof commentFormSchema>) => {
    const payload: ICommentCreatePayload = {
      assignmentId: data.assignmentId,
      consultantId: data.consultantId,
      content: data.content,
    };

    const response = await createEntity<ICommentCreatePayload, IComment>("/v1/comments", payload);
    if (response.error || !response.data) {
      addToast({
        type: "error",
        title: "Comment creation failed",
        description: response.error || "Failed to create comment"
      });
      return;
    }

    addToast({
      type: "success",
      title: "Comment created",
      description: "The comment has been created successfully."
    });
    
    onSuccess?.();
    onCancel();
  };

  const handleEdit = async (data: z.infer<typeof commentFormSchema>) => {
    if (!data.id) {
      addToast({
        type: "error",
        title: "Edit failed",
        description: "Comment ID is missing."
      });
      return;
    }

    const payload: ICommentUpdatePayload = {
      content: data.content,
    };

    const response = await updateEntity<ICommentUpdatePayload, IComment>(`/v1/comments/${data.id}`, payload);
    if (response.error || !response.data) {
      addToast({
        type: "error",
        title: "Comment update failed",
        description: response.error || "Failed to update comment"
      });
      return;
    }

    addToast({
      type: "success",
      title: "Comment updated",
      description: "The comment has been updated successfully."
    });
    
    onSuccess?.();
    onCancel();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="content"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Comment</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter your comment..."
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            {mode === "create" ? "Add Comment" : "Update Comment"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
